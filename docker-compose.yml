version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - .:/app
      - ./mediafiles:/app/mediafiles
    ports:
      - "8000:8000"
    environment:
      - CELERY_BROKER_REDIS_URL=redis://redis:6379/0
      - IMAGEMAGICK_BINARY=/usr/bin/convert
    depends_on:
      - redis

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

networks:
  default:
    driver: bridge
