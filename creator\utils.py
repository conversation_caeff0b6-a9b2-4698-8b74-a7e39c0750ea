"""
Video processing utilities for VIGEN platform.

This module provides functions for video download, processing, transcription,
and AI-powered content generation.
"""

import os
import io
import glob
import random
import tempfile
import logging
import subprocess
import requests
import srt
from typing import Optional, Tuple, List, Union

from django.conf import settings
from pytubefix import YouTube
from moviepy.editor import (
    VideoFileClip,
    CompositeVideoClip,
    AudioFileClip,
    ImageClip,
    clips_array
)
from moviepy.video.tools.subtitles import SubtitlesClip
from moviepy.video.VideoClip import TextClip
from elevenlabs import set_api_key
import assemblyai as aai
from pydub import AudioSegment

from website.models import PodcastEntry, Account



# Configuration

ELEVENLABS_API_KEY = os.getenv('ELEVENLABS_API_KEY')
ASSEMBLYAI_API_KEY = os.getenv('ASSEMBLYAI_API_KEY')
set_api_key(ELEVENLABS_API_KEY)
aai.settings.api_key = ASSEMBLYAI_API_KEY
transcriber = aai.Transcriber()
media_root = settings.MEDIA_ROOT

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')



def save_final_video(final_video: VideoFileClip, output_dir: Optional[str] = None) -> Optional[str]:
    """
    Saves the final video to a specified output directory.

    Args:
        final_video: The video clip object to be saved.
        output_dir: The directory where the final video will be saved.
                   Defaults to media_root/creator/category/test/output/

    Returns:
        Path to the saved video file or None if error occurs

    Raises:
        ValueError: If final_video is None or invalid
    """
    if final_video is None:
        raise ValueError("final_video cannot be None")

    try:
        if output_dir is None:
            output_dir = os.path.join(str(media_root), 'creator', 'category', 'test', 'output')

        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)

        # Generate unique filename
        file_count = count_files_in_folder(output_dir)
        clip_name = f"{file_count}.mp4"
        output_path = os.path.join(output_dir, clip_name)

        # Write video file
        final_video.write_videofile(output_path, codec='libx265')
        final_video.close()

        logging.info(f"Video saved successfully to: {output_path}")
        return output_path

    except Exception as e:
        logging.error(f"Error saving video: {e}")
        if final_video:
            try:
                final_video.close()
            except:
                pass
        return None


def count_files_in_folder(folder_path: str) -> int:
    """
    Counts the number of files in a given folder.

    Args:
        folder_path: The path to the folder whose files need to be counted.

    Returns:
        The number of files in the folder, or 0 if an error occurs.
    """
    try:
        files = os.listdir(folder_path)
        file_count = len([f for f in files if os.path.isfile(os.path.join(folder_path, f))])
        return file_count
    except OSError as e:
        logging.error(f"Error counting files in {folder_path}: {e}")
        return 0  # or an appropriate error code

def select_random_video() -> Optional[str]:
    """
    Selects a random video file from the gameplays directory.

    Returns:
        Full path to a random video file, or None if no videos found

    Raises:
        OSError: If directory doesn't exist or can't be accessed
    """
    try:
        videos_path = os.path.join(media_root, 'creator/gameplays')

        # Check if directory exists
        if not os.path.exists(videos_path):
            logging.error(f"Videos directory does not exist: {videos_path}")
            return None

        # Get list of video files
        try:
            all_files = os.listdir(videos_path)
        except PermissionError:
            logging.error(f"Permission denied accessing directory: {videos_path}")
            return None

        video_extensions = ('.mp4', '.avi', '.mkv', '.mov', '.MP4', '.AVI', '.MKV', '.MOV')
        video_files = [file for file in all_files if file.endswith(video_extensions)]

        if not video_files:
            logging.warning(f"No video files found in {videos_path}")
            return None

        # Select a random video file from the list
        random_video_file = random.choice(video_files)
        full_path = os.path.join(videos_path, random_video_file)

        # Verify the file exists and is readable
        if not os.path.isfile(full_path):
            logging.error(f"Selected video file does not exist: {full_path}")
            return None

        logging.debug(f"Selected random video: {full_path}")
        return full_path

    except Exception as e:
        logging.error(f"Error selecting random video: {e}")
        return None



def get_random_clip_from_videos(clip_duration: Union[int, float]) -> Optional[VideoFileClip]:
    """
    Retrieves a random clip of specified duration from a randomly selected video.

    Args:
        clip_duration: Duration of the clip in seconds (must be positive).

    Returns:
        A VideoFileClip object of the random clip, or None if an error occurs.

    Raises:
        ValueError: If clip_duration is invalid
    """
    # Validate input
    if not isinstance(clip_duration, (int, float)) or clip_duration <= 0:
        raise ValueError("clip_duration must be a positive number")

    if clip_duration > 3600:  # 1 hour limit
        raise ValueError("clip_duration cannot exceed 3600 seconds (1 hour)")

    try:
        video_path = select_random_video()
        if not video_path:
            logging.warning("No video found for extracting a clip.")
            return None

        # Load video with error handling
        try:
            video = VideoFileClip(video_path)
        except Exception as e:
            logging.error(f"Error loading video file {video_path}: {e}")
            return None

        # Check if video duration is sufficient
        if video.duration < clip_duration:
            logging.warning(f"Video {video_path} duration ({video.duration}s) is shorter than requested clip duration ({clip_duration}s)")
            video.close()
            return None

        # Calculate random start time
        max_start = max(0, video.duration - clip_duration)
        start_time = random.uniform(0, max_start)

        # Create and return the clip
        try:
            clip = video.subclip(start_time, start_time + clip_duration)
            logging.debug(f"Created clip from {video_path}: {start_time}s to {start_time + clip_duration}s")
            return clip
        except Exception as e:
            logging.error(f"Error creating clip from video {video_path}: {e}")
            video.close()
            return None

    except Exception as e:
        logging.error(f"Unexpected error in get_random_clip_from_videos: {e}")
        return None



def load_test_logos_and_clips(duration, image_extensions=["*.png", "*.jpg", "*.jpeg"]):
    try:
        images = []
        video_clips = []
        
        img_path = 'creator/category/test/logos'
        pattern = [os.path.join(media_root, img_path, ext) for ext in image_extensions]
        image_files = [file for p in pattern for file in glob.glob(p)]
        
        for image_path in image_files:
            try:
                image_clip = ImageClip(image_path)
                images.append(image_clip)
            except Exception as e:
                logging.error(f"Error loading image: {e}")
        
        for _ in image_files:
            try:
                video_clip = get_random_clip_from_videos(duration)
                video_clips.append(video_clip)
            except Exception as e:
                logging.error(f"Error loading video clip: {e}")

        return images, video_clips
    except Exception as e:
        logging.error(f"Error in load_logos_and_clips: {e}")
        return [], []


def crop_9_16(clip):
    try:
        width, height = clip.size
        if width < 0 or height < 0:
            raise ValueError("Invalid clip dimensions")

        target_width = (9 * height) // 16
        x1 = (width - target_width) // 2
        x2 = x1 + target_width
        return clip.crop(x1=x1, x2=x2, y1=0, y2=height)
    except Exception as e:
        logging.error(f"Error in crop_9_16: {e}")
        return None

def get_audio_clip_from_bytesio(audio_bytesio):
    try:
        with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_audio_file:
            temp_audio_file.write(audio_bytesio.getvalue())
            temp_audio_path = temp_audio_file.name
        return AudioFileClip(temp_audio_path)
    except Exception as e:
        logging.error(f"Error creating audio clip: {e}")
        return None


def crop_video(clip, width, height):
        '''
        Crop a video clip to a specified width and height.

        Parameters:
            clip (VideoClip): The original video clip to be cropped.
            width (int): The desired width of the cropped video.
            height (int): The desired height of the cropped video.

        Returns:
            VideoClip: A new video clip that is a cropped version of the original clip.
        '''

        # Get the dimensions of the original video
        original_width, original_height = clip.size

        # Calculate the coordinates for cropping
        x1 = (original_width - width) // 2
        y1 = (original_height - height) // 2
        x2 = x1 + width
        y2 = y1 + height

        # Crop the video
        cropped_clip = clip.crop(x1=x1, y1=y1, x2=x2, y2=y2)

        return cropped_clip

def generate_voiceover_from_text(text_content, voice_id="DUdKq4ZYPGN7pXABa4qw", speed=1.0):
    """
    Generate voiceover audio from text using ElevenLabs API.

    :param text_content: The text to convert to speech
    :param voice_id: ElevenLabs voice ID to use
    :param speed: Speed multiplier for the audio (1.0 = normal speed)
    :return: Tuple of (audio_clip, audio_data, audio_segment) or (None, None, None) on error
    :raises: ValueError for invalid inputs, requests.RequestException for API errors
    """
    # Validate inputs
    if not text_content or not isinstance(text_content, str):
        raise ValueError("text_content must be a non-empty string")

    if len(text_content.strip()) == 0:
        raise ValueError("text_content cannot be empty or whitespace only")

    if len(text_content) > 5000:  # ElevenLabs has character limits
        raise ValueError("text_content exceeds maximum length of 5000 characters")

    if not isinstance(speed, (int, float)) or speed <= 0 or speed > 3.0:
        raise ValueError("speed must be a number between 0 and 3.0")

    if not ELEVENLABS_API_KEY:
        raise ValueError("ELEVENLABS_API_KEY is not configured")

    try:
        url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"
        headers = {
            "Accept": "audio/mpeg",
            "Content-Type": "application/json",
            "xi-api-key": ELEVENLABS_API_KEY
        }
        data = {
            "text": text_content.strip(),
            "model_id": "eleven_monolingual_v1",
            "voice_settings": {
                "stability": 0.5,
                "similarity_boost": 0.6,
            }
        }

        # Make API request with timeout
        response = requests.post(url, json=data, headers=headers, timeout=30)
        response.raise_for_status()
        audio_data = response.content

        if not audio_data:
            raise ValueError("Received empty audio data from ElevenLabs API")

        # Process audio with speed adjustment
        try:
            audio_segment = AudioSegment.from_file(io.BytesIO(audio_data), format="mp3")

            if speed != 1.0:
                # Adjust speed by changing frame rate
                new_frame_rate = int(audio_segment.frame_rate * speed)
                audio_segment = audio_segment._spawn(
                    audio_segment.raw_data,
                    overrides={"frame_rate": new_frame_rate}
                )

            # Convert to audio clip
            audio_clip = get_audio_clip_from_bytesio(io.BytesIO(audio_segment.export(format='mp3').read()))

            if audio_clip is None:
                raise ValueError("Failed to create audio clip from generated audio")

            logging.info(f"Successfully generated voiceover for text length: {len(text_content)} characters")
            return audio_clip, audio_data, audio_segment

        except Exception as e:
            logging.error(f"Error processing audio data: {e}")
            return None, None, None

    except requests.exceptions.RequestException as e:
        logging.error(f"ElevenLabs API request failed: {e}")
        return None, None, None
    except Exception as e:
        logging.error(f"Unexpected error in generate_voiceover_from_text: {e}")
        return None, None, None

def transcribe_text_from_audio_and_save(audio_data_bytes):

    with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_audio_file:
        temp_audio_file.write(audio_data_bytes)

    temp_audio_path = temp_audio_file.name
    transcript = transcriber.transcribe(temp_audio_path)


    with tempfile.NamedTemporaryFile(suffix='.srt', delete=False) as temp_srt_file:
        temp_srt_file.write(transcript.export_subtitles_srt(chars_per_caption=17).encode('utf-8'))


    #with open(output_dir, 'w') as f:
        #f.writelines(transcript.export_subtitles_srt(chars_per_caption=15))

    temp_audio_file.close()

    return temp_srt_file.name

def generate_story(text):


    voiceover, voiceover_data, voiceover_segment = generate_voiceover_from_text(text) 
    with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_voiceover_file:
        temp_voiceover_file.write(voiceover_data)

    voiceover_duration = voiceover_segment.duration_seconds
    logos, clips = load_test_logos_and_clips(voiceover_duration)
    srt_file = transcribe_text_from_audio_and_save(voiceover_data)  # uses assembly ai api
    

    def generator(text):
        return TextClip(text, font='DejaVu-Sans-Mono-Bold-Oblique', fontsize=60, color='yellow', stroke_color='black', stroke_width=2, method='label')

    subtitles_clip = SubtitlesClip(srt_file, generator).set_position(('center')).set_duration(voiceover_duration)
    
    for i, logo in enumerate(logos):

        video_clip = clips[i].set_audio(voiceover)
        logo = logo.set_end(1).set_start(1).set_duration(voiceover_duration).set_position('bottom')
        final_clip = CompositeVideoClip([video_clip, subtitles_clip, logo])
        save_final_video(final_clip)

def transcribe_text_from_mp3_and_save(audio_file):
    """
    Transcribes text from an MP3 audio file and saves the transcription as an SRT file.

    Args:
        audio_file (str): Path to the input MP3 audio file.

    Returns:
        str: Path to the generated SRT file.
    """

    with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_audio_file:
        audio_file.write_audiofile(temp_audio_file.name, codec='libmp3lame')
        transcript = transcriber.transcribe(temp_audio_file.name)
        with tempfile.NamedTemporaryFile(suffix='.srt', delete=False) as temp_srt_file:
            temp_srt_file.write(transcript.export_subtitles_srt(chars_per_caption=17).encode('utf-8'))
            return temp_srt_file.name
def generate_podcast(url: str):

    # Create a YouTube object
    yt = YouTube(url)

    # Get the highest resolution stream (you can change this based on your preference)
    video_stream = yt.streams.get_highest_resolution()

    # Create temporary files to store the video and subtitles
    with tempfile.NamedTemporaryFile(delete=False, suffix=".mp4") as video_file:
        video_stream.download(output_path='', filename='test')

    subtitle_file = None
    subtitle_stream = yt.streams.filter(file_extension='srt').first()
    if subtitle_stream:
        with tempfile.NamedTemporaryFile(delete=False, suffix=".srt") as subtitle_file:
            subtitle_stream.download(output_path='', filename='test')

    print("Video and subtitles downloaded to temporary files.")
    return video_file, subtitle_file


def check_subtitle_file(file_path):
    # Specify the path to your Go program
    go_program_path = 'subtitle-overlap-fixer'

    # Specify the path to the subtitle file you want to check
    subtitle_file_path = file_path

    try:
        # Run the Go program using subprocess
        result = subprocess.run(
            [go_program_path, subtitle_file_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True  # Capture output as text
        )

        if result.returncode == 0:
            # Success: Go program ran without errors
            output_message = result.stdout
            return output_message
        else:
            # Error: Go program encountered an issue
            error_message = result.stderr
            return error_message

    except Exception as e:
        # Handle any exceptions that may occur during subprocess execution
        return  str(e)
    

def load_account_logos_and_clips(entry: PodcastEntry):
   
    images = []
    video_clips = []
    
    for account in Account.objects.filter(category=entry.podcast.category):
        image_clip = ImageClip(account.logo.path)
        images.append(image_clip)

        video_clip = get_random_clip_from_videos(entry.end_time - entry.start_time)
        video_clips.append(video_clip)
    return images, video_clips

def clip_youtube_and_subtitles(video_path, start_time, end_time, subtitle_path=None):
    """
    Clips a YouTube video and its corresponding subtitles to a specified time range.

    Args:
        video_path (str): Path to the video file
        start_time (int): Start time for clipping in seconds
        end_time (int): End time for clipping in seconds
        subtitle_path (str, optional): Path to the subtitle file (.srt)

    Returns:
        tuple: (clipped_video_clip, clipped_subtitle_path) or (clipped_video_clip, None)
    """
    try:
        # Load the video and create a clip
        video_clip = VideoFileClip(video_path)

        # Validate time bounds
        if start_time < 0:
            start_time = 0
        if end_time > video_clip.duration:
            end_time = video_clip.duration
        if start_time >= end_time:
            raise ValueError("Start time must be less than end time")

        # Create the clipped video
        clipped_video = video_clip.subclip(start_time, end_time)

        # Handle subtitles if provided
        clipped_subtitle_path = None
        if subtitle_path and os.path.exists(subtitle_path):
            clipped_subtitle_path = _clip_subtitle_file(subtitle_path, start_time, end_time)

        return clipped_video, clipped_subtitle_path

    except Exception as e:
        logging.error(f"Error clipping video and subtitles: {e}")
        return None, None


def _clip_subtitle_file(subtitle_path, start_time, end_time):
    """
    Helper function to clip subtitle file to match video segment.

    Args:
        subtitle_path (str): Path to the original subtitle file
        start_time (int): Start time in seconds
        end_time (int): End time in seconds

    Returns:
        str: Path to the clipped subtitle file
    """
    try:
        # Read the original subtitle file
        with open(subtitle_path, 'r', encoding='utf-8') as f:
            subtitle_content = f.read()

        # Parse subtitles
        subtitles = list(srt.parse(subtitle_content))

        # Filter and adjust subtitles for the clipped segment
        clipped_subtitles = []
        for subtitle in subtitles:
            # Convert timedelta to seconds for comparison
            sub_start = subtitle.start.total_seconds()
            sub_end = subtitle.end.total_seconds()

            # Check if subtitle overlaps with our clip
            if sub_end > start_time and sub_start < end_time:
                # Adjust timing relative to the clip start
                new_start = max(0, sub_start - start_time)
                new_end = min(end_time - start_time, sub_end - start_time)

                # Create new subtitle with adjusted timing
                new_subtitle = srt.Subtitle(
                    index=len(clipped_subtitles) + 1,
                    start=srt.timedelta(seconds=new_start),
                    end=srt.timedelta(seconds=new_end),
                    content=subtitle.content
                )
                clipped_subtitles.append(new_subtitle)

        # Save clipped subtitles to a temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False, encoding='utf-8') as temp_file:
            temp_file.write(srt.compose(clipped_subtitles))
            return temp_file.name

    except Exception as e:
        logging.error(f"Error clipping subtitle file: {e}")
        return None

def get_top_clip(entry):
    entry.fetch_source_location()
    top_clip = VideoFileClip(entry.podcast.source_location)
    return top_clip

def get_podcast_entry(entry_id):
    entry = PodcastEntry.objects.get(id=entry_id)
    entry.mark_as_processing()
    return entry

def create_logo_clip(account, top_clip):
    return ImageClip(account.logo.path).set_end(1).set_start(1).set_duration(top_clip.duration).set_position('bottom')

def create_bottom_clip(entry, top_clip_width, top_clip_height):
    bottom_clip = get_random_clip_from_videos(entry.end_time - entry.start_time)
    return crop_video(bottom_clip.set_audio(None), top_clip_width, top_clip_height)

def create_final_clip(top_clip, bottom_clip, subtitles_clip, logo):
    final_clip = clips_array([ [top_clip], [bottom_clip] ])
    if subtitles_clip:
        return CompositeVideoClip([final_clip, subtitles_clip, logo])
    else:
        return CompositeVideoClip([final_clip, logo])


def write_final_clip_to_file(final_clip, entry, account, category_dir):
    filename = os.path.join(category_dir, 'output')+ f"/entry-{entry.id}-{account.id}.mp4"
    final_clip.write_videofile(filename, codec='libx264')
    return '/media/' + filename.split('/mediafiles/')[1]
    

def podcast_split_screen(entry_id):
    entry = get_podcast_entry(entry_id)
    top_clip = get_top_clip(entry).subclip(entry.start_time, entry.end_time)
    top_clip_width, top_clip_height = top_clip.size
    category_dir = os.path.join(media_root, 'creator', 'category', entry.podcast.category.name)

    def generator(text):
        color = random.choice(['White','White', 'Yellow'])
        return TextClip(
            text,
            font='Times-Bold',
            fontsize=42,
            color=color,
            stroke_color='black',
            stroke_width=2,
            method='label',
    )
    subtitles_clip = None

    if entry.transcripe:
        srt_file = transcribe_text_from_mp3_and_save(top_clip.audio)
        entry.transcript = srt_file
        subtitles_clip = SubtitlesClip(srt_file, generator).set_position(('center', 'center'))

    accounts = Account.objects.filter(category=entry.podcast.category)
    locations = []
    for account in accounts:
        logo = create_logo_clip(account, top_clip)
        bottom_clip = create_bottom_clip(entry, top_clip_width, top_clip_height)
        final_clip = create_final_clip(top_clip, bottom_clip, subtitles_clip, logo)
        location = write_final_clip_to_file(final_clip, entry, account, category_dir)
        locations.append(location)
    entry.update_locations(locations)


    

    # logos, clips = load_account_logos_and_clips(entry)
    # for i, logo in enumerate(logos):
    #     bottom_clip = get_random_clip_from_videos(entry.end_time - entry.start_time)
    #     bottom_clip = crop_video(bottom_clip.set_audio(None), top_clip_width, top_clip_height)
    #     logo = logo.set_end(1).set_start(1).set_duration(top_clip.duration).set_position('bottom')
    #     final_clip = clips_array([ [top_clip], [bottom_clip] ])
    #     final_clip = CompositeVideoClip([final_clip, subtitles_clip, logo])
    #     output_files_count = count_files_in_folder(category_dir+'/output/')
    #     filename = os.path.join(category_dir, 'output')+f"/{entry.podcast.title}-{str(output_files_count)}.mp4"
    #     final_clip.write_videofile(filename, codec='libx264')
    # entry.mark_as_completed()
        

        
def clean_temporary_directory(temp_dir):
    try:
        if os.path.exists(temp_dir):
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(media_root, temp_dir, filename)
                os.remove(file_path)
            os.rmdir(temp_dir)
            print("Temporary files and directory cleaned up.")
    except Exception as e:
        print(f"An error occurred while cleaning up: {e}")

